#!/bin/bash

# 设置模型路径和端口
MODEL="/home/<USER>/workspace_zyp/DeepSeek-R1-Distill-Llama-70B"
PORT=9269
stress_log_path="./stress_log.log"

# 定义不同的长度和 prompts 数量
# lengths=(2048)  # input_len 和 output_len 长度一致
# num_prompts=(1 4 8 128 256)
num_prompts=(1 4 8 16 32 64)

# 循环所有组合

for num_prompt in "${num_prompts[@]}"; do
  echo "Running benchmark with input_len=$len, output_len=$len, num_prompts=$num_prompt"

  # 运行 benchmark_serving.py
  python3 ./benchmark_serving.py \
    --model $MODEL \
    --dataset-name random \
    --random-input-len 2048 \
    --random-output-len 1024 \
    --num-prompts $num_prompt \
    --trust-remote-code \
    --port $PORT \
    --ignore-eos \
    2>&1 | tee -a $stress_log_path

  echo "Finished benchmark with input_len=$len, output_len=$len, num_prompts=$num_prompt"
  echo "----------------------------------------"
done


echo "Benchmarking completed. Results saved to $stress_log_path"
