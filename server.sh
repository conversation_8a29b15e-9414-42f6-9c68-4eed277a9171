#!/bin/bash

read _ server_log_path _ PORT MODEL  < <(bash setup_env.sh)
echo "$log_path" "$server_log_path" "$stress_log_path" "$PORT" "$MODEL" 
# export VLLM_ENFORCE_CUDA_GRAPH=1

CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 python3 -m vllm.entrypoints.openai.api_server \
 --model $MODEL --tokenizer $MODEL \
 --tensor-parallel-size 8 \
 --disable_log_requests \
 --max-model-len 10240 \
 --trust-remote-code --host "0.0.0.0" --port $PORT \
 | tee $server_log_path


